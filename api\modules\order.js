import request, { analysisRes } from '../request';
import config from '../config';

const { order } = config.apiUrls;

export default {
  /** 查询可接单列表 */
  async list(userId, type) {
    const res = await request.get(order.list.replace('{userId}', userId), {
      hideLoading: true,
      type,
    });
    const data = analysisRes(res);
    return data;
  },

  /** 按状态查询员工名下的订单列表 */
  async myList(employeeId, status) {
    const where = {
      hideLoading: true,
    };
    if (status && status !== 'all') {
      where.status = status;
    }
    const res = await request.get(order.myList.replace('{employeeId}', employeeId), where);
    const data = analysisRes(res);
    return data;
  },

  /** 接单 */
  async accept(orderId, employeeId) {
    const res = await request.post(order.accept.replace('{orderId}', orderId), {
      employeeId,
    });
    const data = analysisRes(res);
    return data;
  },

  // 修改服务时间
  async updateServiceTime(orderId, employeeId, serviceTime) {
    const res = await request.put(order.updateServiceTime.replace('{orderId}', orderId), {
      employeeId,
      serviceTime,
    });
    const data = analysisRes(res);
    return data;
  },

  /** 出发 */
  async dispatch(orderId, employeeId) {
    const res = await request.post(order.dispatch.replace('{orderId}', orderId), {
      employeeId,
    });
    const data = analysisRes(res);
    return data;
  },

  /** 开始服务 */
  async start(orderId, employeeId, beforePhotos = []) {
    const res = await request.post(order.start.replace('{orderId}', orderId), {
      employeeId,
      beforePhotos,
    });
    const data = analysisRes(res);
    return data;
  },

  /** 完成订单 */
  async complete(orderId, employeeId, afterPhotos = []) {
    const res = await request.post(order.complete.replace('{orderId}', orderId), {
      employeeId,
      afterPhotos,
    });
    const data = analysisRes(res);
    return data;
  },

  /** 批量上传服务前照片 */
  async uploadBeforePhotos(orderId, employeeId, photoUrls) {
    const res = await request.post(order.uploadBeforePhotos.replace('{orderId}', orderId), {
      employeeId,
      photoUrls,
    });
    const data = analysisRes(res);
    return data;
  },

  /** 批量上传服务后照片 */
  async uploadAfterPhotos(orderId, employeeId, photoUrls) {
    const res = await request.post(order.uploadAfterPhotos.replace('{orderId}', orderId), {
      employeeId,
      photoUrls,
    });
    const data = analysisRes(res);
    return data;
  },

  /** 查询订单服务照片 */
  async getServicePhotos(orderId) {
    const res = await request.get(order.getServicePhotos.replace('{orderId}', orderId), {
      hideLoading: true,
    });
    const data = analysisRes(res);
    return data;
  },
};
